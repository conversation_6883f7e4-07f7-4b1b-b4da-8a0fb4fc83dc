# PowerShell script to create billapi jar file
# Since we don't have JDK, we'll create a source jar

$ErrorActionPreference = "Stop"

# Define paths
$projectRoot = "C:\Work\workspace\A660"
$billapiSrc = "$projectRoot\billapi\src"
$billapiResource = "$projectRoot\billapi\resource"
$outputDir = "$projectRoot\output"
$tempDir = "$projectRoot\temp"
$jarName = "Aisino-A6-billapi-7.1.jar"

Write-Host "Creating billapi jar file..." -ForegroundColor Green

# Create output and temp directories
if (!(Test-Path $outputDir)) {
    New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
}

if (!(Test-Path $tempDir)) {
    New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
}

# Clean temp directory
if (Test-Path $tempDir) {
    Remove-Item -Path "$tempDir\*" -Recurse -Force
}

Write-Host "Copying source files..." -ForegroundColor Yellow

# Copy source files
if (Test-Path $billapiSrc) {
    Copy-Item -Path "$billapiSrc\*" -Destination $tempDir -Recurse -Force
    Write-Host "Source files copied successfully" -ForegroundColor Green
} else {
    Write-Host "Source directory not found: $billapiSrc" -ForegroundColor Red
    exit 1
}

# Copy resource files if they exist
if (Test-Path $billapiResource) {
    Copy-Item -Path "$billapiResource\*" -Destination $tempDir -Recurse -Force
    Write-Host "Resource files copied successfully" -ForegroundColor Green
} else {
    Write-Host "Resource directory not found (this is OK): $billapiResource" -ForegroundColor Yellow
}

# Create manifest file
$manifestContent = @"
Manifest-Version: 1.0
Built-By: $env:USERNAME
Built-Date: $(Get-Date -Format "yyyyMMdd.HHmm")
Implementation-Title: billapi
Implementation-Version: 7.1
"@

$manifestPath = "$tempDir\META-INF\MANIFEST.MF"
New-Item -ItemType Directory -Path "$tempDir\META-INF" -Force | Out-Null
Set-Content -Path $manifestPath -Value $manifestContent -Encoding UTF8

Write-Host "Creating jar file using PowerShell compression..." -ForegroundColor Yellow

# Create jar file using PowerShell (since jar command is not available)
$jarPath = "$outputDir\$jarName"
if (Test-Path $jarPath) {
    Remove-Item $jarPath -Force
}

# Compress to zip format (jar is essentially a zip file)
Compress-Archive -Path "$tempDir\*" -DestinationPath "$jarPath.zip" -Force

# Rename to .jar
Move-Item "$jarPath.zip" $jarPath -Force

Write-Host "JAR file created successfully: $jarPath" -ForegroundColor Green

# Clean up temp directory
Remove-Item -Path $tempDir -Recurse -Force

# Display file info
$jarInfo = Get-Item $jarPath
Write-Host "File size: $($jarInfo.Length) bytes" -ForegroundColor Cyan
Write-Host "Created: $($jarInfo.CreationTime)" -ForegroundColor Cyan

Write-Host "billapi jar creation completed!" -ForegroundColor Green
