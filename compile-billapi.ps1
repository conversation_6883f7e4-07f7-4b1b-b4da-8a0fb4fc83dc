# Simple PowerShell script to compile billapi with Java 1.7
Write-Host "开始使用 Java 1.7 编译 billapi..." -ForegroundColor Green

# 设置路径
$javaHome = "C:\Work\java\jdk\jdk1.7.0_06"
$projectRoot = "C:\Work\workspace\A660"
$billapiSrc = "$projectRoot\billapi\src"
$outputDir = "$projectRoot\output"
$buildDir = "$projectRoot\build\classes\billapi"

# 检查 Java 1.7
if (!(Test-Path "$javaHome\bin\javac.exe")) {
    Write-Host "错误: 找不到 Java 1.7 编译器" -ForegroundColor Red
    exit 1
}

# 检查源码目录
if (!(Test-Path $billapiSrc)) {
    Write-Host "错误: 找不到源码目录 $billapiSrc" -ForegroundColor Red
    exit 1
}

# 创建输出目录
if (!(Test-Path $buildDir)) {
    New-Item -ItemType Directory -Path $buildDir -Force | Out-Null
}
if (!(Test-Path $outputDir)) {
    New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
}

# 清理构建目录
if (Test-Path $buildDir) {
    Remove-Item -Path "$buildDir\*" -Recurse -Force -ErrorAction SilentlyContinue
}

Write-Host "源码目录: $billapiSrc" -ForegroundColor Cyan
Write-Host "构建目录: $buildDir" -ForegroundColor Cyan

# 查找 Java 文件
$javaFiles = Get-ChildItem -Path $billapiSrc -Filter "*.java" -Recurse
Write-Host "找到 $($javaFiles.Count) 个 Java 文件" -ForegroundColor Yellow

if ($javaFiles.Count -eq 0) {
    Write-Host "没有找到 Java 源文件" -ForegroundColor Red
    exit 1
}

# 编译
Write-Host "开始编译..." -ForegroundColor Yellow
$env:JAVA_HOME = $javaHome
$env:PATH = "$javaHome\bin;$env:PATH"

# 创建源文件列表文件
$sourceListFile = "$projectRoot\build\sources.txt"
$javaFiles | ForEach-Object { $_.FullName } | Out-File -FilePath $sourceListFile -Encoding UTF8

# 使用简单的编译命令
$compileCmd = "`"$javaHome\bin\javac`" -cp `"$projectRoot\classes;.`" -d `"$buildDir`" -encoding UTF-8 -source 1.7 -target 1.7 @`"$sourceListFile`""

Write-Host "执行编译命令..." -ForegroundColor Cyan
Write-Host $compileCmd -ForegroundColor Gray
Invoke-Expression $compileCmd

if ($LASTEXITCODE -eq 0) {
    Write-Host "编译成功!" -ForegroundColor Green
    
    # 创建 JAR
    Write-Host "创建 JAR 文件..." -ForegroundColor Yellow
    $jarPath = "$outputDir\Aisino-A6-billapi-7.1.jar"
    
    $jarCmd = "jar cf `"$jarPath`" -C `"$buildDir`" ."
    Invoke-Expression $jarCmd
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "JAR 文件创建成功: $jarPath" -ForegroundColor Green
        
        if (Test-Path $jarPath) {
            $jarInfo = Get-Item $jarPath
            Write-Host "文件大小: $($jarInfo.Length) 字节" -ForegroundColor Cyan
        }
    } else {
        Write-Host "JAR 创建失败" -ForegroundColor Red
    }
} else {
    Write-Host "编译失败" -ForegroundColor Red
}
