@echo off
setlocal enabledelayedexpansion

echo ========================================
echo 使用 Java 1.7 编译和打包 billapi
echo ========================================

:: 设置 Java 1.7 路径
set JAVA_HOME=C:\Work\java\jdk\jdk1.7.0_06
set PATH=%JAVA_HOME%\bin;%PATH%

:: 设置项目路径
set PROJECT_ROOT=%~dp0
set BILLAPI_SRC=%PROJECT_ROOT%billapi\src
set BILLAPI_RESOURCE=%PROJECT_ROOT%billapi\resource
set BUILD_DIR=%PROJECT_ROOT%build
set CLASSES_DIR=%BUILD_DIR%\classes\billapi
set OUTPUT_DIR=%PROJECT_ROOT%output
set LIB_DIR=%PROJECT_ROOT%lib

:: 创建必要的目录
echo 创建构建目录...
if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"
if not exist "%CLASSES_DIR%" mkdir "%CLASSES_DIR%"
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"

:: 清理之前的构建
echo 清理之前的构建文件...
if exist "%CLASSES_DIR%" rmdir /s /q "%CLASSES_DIR%"
mkdir "%CLASSES_DIR%"

:: 检查源码目录
if not exist "%BILLAPI_SRC%" (
    echo 错误: 找不到源码目录 %BILLAPI_SRC%
    pause
    exit /b 1
)

echo 源码目录: %BILLAPI_SRC%
echo 输出目录: %CLASSES_DIR%

:: 构建 classpath
set CLASSPATH=.
if exist "%LIB_DIR%" (
    echo 添加 lib 目录到 classpath...
    for %%f in ("%LIB_DIR%\*.jar") do (
        set CLASSPATH=!CLASSPATH!;%%f
    )
)

:: 添加当前项目的 classes 目录
if exist "%PROJECT_ROOT%classes" (
    set CLASSPATH=!CLASSPATH!;%PROJECT_ROOT%classes
)

echo 使用的 classpath: %CLASSPATH%

:: 编译 Java 源码
echo 开始编译 billapi 源码...
javac -cp "%CLASSPATH%" -d "%CLASSES_DIR%" -encoding UTF-8 -source 1.7 -target 1.7 -Xlint:deprecation "%BILLAPI_SRC%\com\aisino\*.java" "%BILLAPI_SRC%\com\aisino\*\*.java" "%BILLAPI_SRC%\com\aisino\*\*\*.java" "%BILLAPI_SRC%\com\aisino\*\*\*\*.java" 2>compile_errors.log

if %ERRORLEVEL% neq 0 (
    echo 编译失败，请检查 compile_errors.log 文件
    type compile_errors.log
    pause
    exit /b 1
)

echo 编译成功！

:: 复制资源文件
if exist "%BILLAPI_RESOURCE%" (
    echo 复制资源文件...
    xcopy "%BILLAPI_RESOURCE%\*" "%CLASSES_DIR%\" /s /e /y
)

:: 创建 JAR 文件
set JAR_NAME=Aisino-A6-billapi-7.1.jar
set JAR_PATH=%OUTPUT_DIR%\%JAR_NAME%

echo 创建 JAR 文件: %JAR_NAME%

:: 创建 manifest 文件
set MANIFEST_FILE=%BUILD_DIR%\MANIFEST.MF
echo Manifest-Version: 1.0 > "%MANIFEST_FILE%"
echo Built-By: %USERNAME% >> "%MANIFEST_FILE%"
echo Built-Date: %DATE% %TIME% >> "%MANIFEST_FILE%"
echo Implementation-Title: billapi >> "%MANIFEST_FILE%"
echo Implementation-Version: 7.1 >> "%MANIFEST_FILE%"
echo. >> "%MANIFEST_FILE%"

:: 打包 JAR
jar cfm "%JAR_PATH%" "%MANIFEST_FILE%" -C "%CLASSES_DIR%" .

if %ERRORLEVEL% neq 0 (
    echo JAR 打包失败
    pause
    exit /b 1
)

echo ========================================
echo 构建完成！
echo JAR 文件位置: %JAR_PATH%
echo ========================================

:: 显示文件信息
if exist "%JAR_PATH%" (
    dir "%JAR_PATH%"
)

pause
