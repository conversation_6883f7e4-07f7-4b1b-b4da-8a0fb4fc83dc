<?xml version="1.0"?>

<project default="package" basedir=".">

	<!-- Name of project and version -->
	<property name="com.name" value="Aisino" /> 	<!-- 公司名 -->
	<property name="proj.name" value="A6" />		<!-- 项目名 -->
	<property name="module.name" value="orderimport" />	<!-- 模块名 -->
	<property name="module.version" value="6.0" />	<!-- 模块名 -->
	
	<!-- Global properties for this build -->
	<property name="my.src.java.dir" location="sa/orderimport/src" />						<!-- 源码目录 -->
	<property name="my.src.res.dir" location="sa/orderimport/resource" />					<!-- 配置文件目录 -->
	<property name="lib.dir" location="webapp/WEB-INF/lib" />				<!-- lib目录 -->
	<property name="tomcat.lib.dir" location="C:\Work\tomcat\apache-tomcat-8.5.39\lib"/><!-- Tom<PERSON>的lib目录 -->
	
		
	<property name="build.dir" location="bin/temp" />
	<property name="my.build.classes.dir" location="${build.dir}/classes" />
	<property name="jar.dir" location="." />
	<property name="remote.java.dir" location="."/>
	<property name="remote.web.dir" location="."/>
	<!--
	<property name="remote.java.dir" location="\\leo\pub\java"/>
	<property name="remote.web.dir" location="\\leo\pub\web"/>
	-->
	

	<tstamp prefix="build.">
	   <format property="TimeSign" pattern="MM-dd-HH-mm"/>
	</tstamp>
	<!-- Classpath declaration -->
	<path id="project.classpath">
		<fileset dir="${lib.dir}">
			<include name="**/*.jar" />
			<include name="**/*.zip" />
		</fileset>
		<fileset dir="${tomcat.lib.dir}">
			<include name="**/*.jar" />
		</fileset>
	</path>

	<!-- Useful shortcuts -->
	<patternset id="meta.files">
		<include name="**/*.xml" />
		<include name="**/*.dtd" />
	</patternset>


	<!-- Clean up -->
	<target name="clean" description="Clean the build directory">
		<delete dir="${build.dir}" />
		<mkdir dir="${build.dir}" />
	</target>

	<!-- Compile Java source -->
	<target name="compile" depends="clean">
		<mkdir dir="${my.build.classes.dir}" />

		<echo>编译类文件,来自: ${my.src.java.dir}</echo>
		<javac source="1.5" srcdir="${my.src.java.dir}" listfiles="true" encoding="utf8" nowarn="true" destdir="${my.build.classes.dir}" classpathref="project.classpath">
			<!-- 如果源码目录下有需要排除的类文件 --> 
			<exclude name="com/aisino/platform/**" />
			<exclude name="com/aisino/u3/finance/print/plugin/**" />
			<exclude name="com/aisino/u3/finance/gl/acctset/**" />
		</javac>

		<echo>复制资源文件,来自: ${my.src.res.dir}</echo>
		<copy todir="${my.build.classes.dir}">
			<fileset dir="${my.src.res.dir}">
				<patternset refid="meta.files" />
			</fileset>
		</copy>
		
		<!--	
		<echo>复制java源码,来自: ${my.src.java.dir}</echo>
		<copy todir="${my.build.classes.dir}">
		<fileset dir="${my.src.java.dir}">
		<include name="**/*.java"/>
		</fileset>
		</copy>
		-->	
		
	</target>

	<!-- Compile Java source -->
	<!-- package classes and resources -->
	<target name="package" depends="compile">
		<echo>正在打包类文件</echo>
		<mkdir dir="${remote.java.dir}" /> 
		<jar jarfile="${remote.java.dir}/${com.name}-${proj.name}-${module.name}-${module.version}.jar">
			<fileset dir="${my.build.classes.dir}" >
				<!-- 如果源码目录下有需要排除的类文件 -->
				<exclude name="com/aisino/platform/**" />
				<exclude name="com/aisino/u3/finance/print/plugin/**" />
				<exclude name="com/aisino/u3/finance/gl/acctset/**" />
			</fileset>
		</jar>
 
		<!-- 如果有webapp下的文件需要发布,使用正面的部分.对内容做相应修改
		<echo>正在打包页面文件</echo>
		<mkdir dir="${remote.web.dir}" /> 
		<zip destfile="${remote.web.dir}/${com.name}-${proj.name}-${module.name}-${module.version}.zip">
			<zipfileset dir="webapp/pt/mainui" includes="**/*.*" prefix="webapp/pt/mainui"/>
			<zipfileset dir="webapp/pt/js" includes="**/*.js" prefix="webapp/pt/js"/>
			<zipfileset dir="webapp/pt" includes="login.html" prefix="webapp/pt"/>
		</zip>
		-->
		
		<echo>正在打包页面文件</echo>
		<mkdir dir="${remote.web.dir}" /> 
		<zip destfile="${remote.web.dir}/${com.name}-${proj.name}-${module.name}-${module.version}.zip">
			<zipfileset dir="webapp/a" includes="*.cll" prefix="webapp"/>
			
		</zip>
		

		<delete dir="${build.dir}"></delete>
		<echo>发布成功</echo>
	</target>


</project>
