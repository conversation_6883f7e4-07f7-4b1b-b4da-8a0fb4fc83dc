<?xml version="1.0" ?>

<project default="build-billapi" basedir=".">
	<tstamp>
	   <format property="TODAY_UK_M" pattern="yyyyMMdd" locale="en"/>
	   <format property="TODAY_UK" pattern="yyyyMMdd.HHmm" locale="en"/> 
	</tstamp>

	<!-- Name of project and version -->
	<property name="com.name" value="Aisino" />
	<property name="proj.name" value="A6" />
	<property name="module.name" value="billapi" />
	<property name="module.version" value="7.1" />

	<!-- Global properties for this build -->
	<property name="java.home" location="C:\Work\java\jdk\jdk1.7.0_06" />
	<property name="lib.dir" location="lib" />
	<property name="build.dir" location="${basedir}/build" />
	<property name="output.dir" location="${basedir}/output" />
	<property name="my.build.classes.dir" location="${build.dir}/classes" />

	<!-- Classpath declaration -->
	<path id="project.classpath">
		<fileset dir="${lib.dir}" erroronmissingdir="false">
			<include name="**/*.jar" />
		</fileset>
		<!-- 添加当前项目的classes目录 -->
		<pathelement location="${basedir}/classes" />
		<!-- 添加系统classpath -->
		<pathelement path="${java.class.path}" />
	</path>

	<!-- Useful shortcuts -->
	<patternset id="meta.files">
		<include name="**/*.xml" />
		<include name="**/*.dtd" />
		<include name="**/*.js" />
		<include name="**/*.css" />
		<include name="**/*.gif" />
		<include name="**/*.sql" />
		<include name="**/*.jpg" />
		<include name="**/*.png" />
		<include name="**/*.xls" />
		<include name="**/*.cll" />
		<include name="**/*.vbs" />
		<include name="**/*.html" />
		<include name="**/*.cab" />
		<include name="**/*.jpeg" />
		<include name="**/*.properties" />
	</patternset>

	<target name="init">
		<echo>初始化构建环境...</echo>
		<mkdir dir="${build.dir}" />
		<mkdir dir="${my.build.classes.dir}" />
		<mkdir dir="${output.dir}" />
	</target>

	<target name="clean">
		<echo>清理构建目录...</echo>
		<delete dir="${build.dir}" />
		<delete dir="${output.dir}" />
	</target>

	<target name="compile" depends="init">
		<echo>编译billapi源码...</echo>
		<mkdir dir="${my.build.classes.dir}/${module.name}" />

		<javac srcdir="${basedir}/${module.name}/src"
			   destdir="${my.build.classes.dir}/${module.name}"
			   encoding="utf-8"
			   includeantruntime="false"
			   debug="on"
			   failonerror="false"
			   source="1.7"
			   target="1.7"
			   fork="true"
			   executable="${java.home}/bin/javac">
			<classpath refid="project.classpath" />
		</javac>
	</target>

	<target name="jar" depends="compile">
		<echo>打包billapi JAR文件...</echo>
		
		<jar jarfile="${output.dir}/${com.name}-${proj.name}-${module.name}-${module.version}.jar">
			<!-- 包含编译后的class文件 -->
			<fileset dir="${my.build.classes.dir}/${module.name}">
				<include name="**/*.class" />
			</fileset>
			
			<!-- 包含资源文件 -->
			<fileset dir="${basedir}/${module.name}/resource" erroronmissingdir="false">
				<patternset refid="meta.files" />
			</fileset>
			
			<!-- 添加manifest -->
			<manifest>
				<attribute name="Built-By" value="${user.name}" />
				<attribute name="Built-Date" value="${TODAY_UK}" />
				<attribute name="Implementation-Title" value="${module.name}" />
				<attribute name="Implementation-Version" value="${module.version}" />
			</manifest>
		</jar>
		
		<echo>JAR文件已生成: ${output.dir}/${com.name}-${proj.name}-${module.name}-${module.version}.jar</echo>
	</target>

	<target name="build-billapi" depends="clean,jar">
		<echo>billapi构建完成!</echo>
		<echo>输出文件: ${output.dir}/${com.name}-${proj.name}-${module.name}-${module.version}.jar</echo>
	</target>

</project>
