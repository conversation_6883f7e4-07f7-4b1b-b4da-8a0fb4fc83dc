# Simple compilation script
Write-Host "编译 billapi 使用 Java 1.7..." -ForegroundColor Green

$javaHome = "C:\Work\java\jdk\jdk1.7.0_06"
$javac = "$javaHome\bin\javac.exe"
$jar = "$javaHome\bin\jar.exe"

# 设置环境变量
$env:JAVA_HOME = $javaHome
$env:PATH = "$javaHome\bin;$env:PATH"

# 创建构建目录
$buildDir = "build\classes\billapi"
if (!(Test-Path $buildDir)) {
    New-Item -ItemType Directory -Path $buildDir -Force | Out-Null
}

# 清理构建目录
Remove-Item -Path "$buildDir\*" -Recurse -Force -ErrorAction SilentlyContinue

Write-Host "查找 Java 源文件..." -ForegroundColor Yellow
$javaFiles = Get-ChildItem -Path "billapi\src" -Filter "*.java" -Recurse

Write-Host "找到 $($javaFiles.Count) 个 Java 文件" -ForegroundColor Cyan

# 构建 classpath
Write-Host "构建 classpath..." -ForegroundColor Yellow
$classpath = "classes;."

# 添加项目中其他模块的源码目录
$sourceModules = @(
    "A6COMMON\src",
    "SA\util\src",
    "PU\util\src",
    "BUSBASE\common\src"
)

foreach ($module in $sourceModules) {
    if (Test-Path $module) {
        $classpath += ";$module"
        Write-Host "添加源码模块: $module" -ForegroundColor Gray
    }
}

# 添加 webapp/WEB-INF/lib 中的所有 JAR 文件
$libDir = "webapp\WEB-INF\lib"
if (Test-Path $libDir) {
    $jarFiles = Get-ChildItem -Path $libDir -Filter "*.jar"
    foreach ($jar in $jarFiles) {
        $classpath += ";$($jar.FullName)"
    }
    Write-Host "添加了 $($jarFiles.Count) 个 JAR 文件到 classpath" -ForegroundColor Cyan
}

# 编译所有文件一次性
Write-Host "开始编译..." -ForegroundColor Yellow
$sourceFiles = $javaFiles | ForEach-Object { "`"$($_.FullName)`"" }
$sourceFilesStr = $sourceFiles -join " "

Write-Host "编译 $($javaFiles.Count) 个 Java 文件..." -ForegroundColor Gray
& $javac -cp $classpath -d $buildDir -encoding UTF-8 -source 1.7 -target 1.7 $sourceFiles

if ($LASTEXITCODE -ne 0) {
    Write-Host "编译失败" -ForegroundColor Red
    exit 1
}

Write-Host "编译完成!" -ForegroundColor Green

# 创建 JAR
Write-Host "创建 JAR 文件..." -ForegroundColor Yellow
$jarPath = "output\Aisino-A6-billapi-7.1.jar"

# 确保输出目录存在
if (!(Test-Path "output")) {
    New-Item -ItemType Directory -Path "output" -Force | Out-Null
}

& $jar cf $jarPath -C $buildDir .

if ($LASTEXITCODE -eq 0) {
    Write-Host "JAR 文件创建成功: $jarPath" -ForegroundColor Green
    
    if (Test-Path $jarPath) {
        $jarInfo = Get-Item $jarPath
        Write-Host "文件大小: $($jarInfo.Length) 字节" -ForegroundColor Cyan
        Write-Host "创建时间: $($jarInfo.CreationTime)" -ForegroundColor Cyan
    }
} else {
    Write-Host "JAR 创建失败" -ForegroundColor Red
}

Write-Host "完成!" -ForegroundColor Green
