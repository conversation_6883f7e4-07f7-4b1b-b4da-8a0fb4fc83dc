# 只编译 billapi 模块的脚本
Write-Host "编译 billapi 模块使用 Java 1.7..." -ForegroundColor Green

$javaHome = "C:\Work\java\jdk\jdk1.7.0_06"
$javac = "$javaHome\bin\javac.exe"
$jar = "$javaHome\bin\jar.exe"

# 设置环境变量
$env:JAVA_HOME = $javaHome
$env:PATH = "$javaHome\bin;$env:PATH"

# 创建构建目录
$buildDir = "build\classes\billapi"
if (!(Test-Path $buildDir)) {
    New-Item -ItemType Directory -Path $buildDir -Force | Out-Null
}

# 清理构建目录
Remove-Item -Path "$buildDir\*" -Recurse -Force -ErrorAction SilentlyContinue

Write-Host "查找 billapi Java 源文件..." -ForegroundColor Yellow
$javaFiles = Get-ChildItem -Path "billapi\src" -Filter "*.java" -Recurse

Write-Host "找到 $($javaFiles.Count) 个 Java 文件" -ForegroundColor Cyan

# 构建 classpath - 只包含已编译的类和 JAR 文件
Write-Host "构建 classpath..." -ForegroundColor Yellow
$classpath = "classes;."

# 添加 webapp/WEB-INF/lib 中的所有 JAR 文件
$libDir = "webapp\WEB-INF\lib"
if (Test-Path $libDir) {
    $jarFiles = Get-ChildItem -Path $libDir -Filter "*.jar"
    foreach ($jar in $jarFiles) {
        $classpath += ";$($jar.FullName)"
    }
    Write-Host "添加了 $($jarFiles.Count) 个 JAR 文件到 classpath" -ForegroundColor Cyan
}

# 编译 billapi 文件，忽略错误
Write-Host "开始编译 billapi..." -ForegroundColor Yellow
$sourceFiles = $javaFiles | ForEach-Object { "`"$($_.FullName)`"" }

Write-Host "编译 $($javaFiles.Count) 个 Java 文件..." -ForegroundColor Gray
& $javac -cp $classpath -d $buildDir -encoding UTF-8 -source 1.7 -target 1.7 -Xlint:none $sourceFiles

# 检查是否有编译成功的类文件
$classFiles = Get-ChildItem -Path $buildDir -Filter "*.class" -Recurse
Write-Host "编译生成了 $($classFiles.Count) 个 class 文件" -ForegroundColor Cyan

if ($classFiles.Count -gt 0) {
    Write-Host "部分编译成功，继续创建 JAR..." -ForegroundColor Yellow
    
    # 复制资源文件
    $resourceDir = "billapi\resource"
    if (Test-Path $resourceDir) {
        Write-Host "复制资源文件..." -ForegroundColor Yellow
        Copy-Item -Path "$resourceDir\*" -Destination $buildDir -Recurse -Force -ErrorAction SilentlyContinue
    }
    
    # 创建 JAR
    Write-Host "创建 JAR 文件..." -ForegroundColor Yellow
    $jarPath = "output\Aisino-A6-billapi-7.1.jar"
    
    # 确保输出目录存在
    if (!(Test-Path "output")) {
        New-Item -ItemType Directory -Path "output" -Force | Out-Null
    }
    
    # 创建 manifest 文件
    $manifestContent = @"
Manifest-Version: 1.0
Built-By: $env:USERNAME
Built-Date: $(Get-Date -Format "yyyyMMdd.HHmm")
Implementation-Title: billapi
Implementation-Version: 7.1
Java-Version: 1.7

"@
    
    $manifestFile = "build\MANIFEST.MF"
    Set-Content -Path $manifestFile -Value $manifestContent -Encoding UTF8
    
    & $jar cfm $jarPath $manifestFile -C $buildDir .
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "JAR 文件创建成功: $jarPath" -ForegroundColor Green
        
        if (Test-Path $jarPath) {
            $jarInfo = Get-Item $jarPath
            Write-Host "文件大小: $($jarInfo.Length) 字节" -ForegroundColor Cyan
            Write-Host "创建时间: $($jarInfo.CreationTime)" -ForegroundColor Cyan
            
            # 显示 JAR 内容
            Write-Host "JAR 文件内容:" -ForegroundColor Yellow
            & $jar tf $jarPath | Select-Object -First 20
            if ((& $jar tf $jarPath | Measure-Object).Count -gt 20) {
                Write-Host "... (还有更多文件)" -ForegroundColor Gray
            }
        }
    } else {
        Write-Host "JAR 创建失败" -ForegroundColor Red
    }
} else {
    Write-Host "没有成功编译的类文件，无法创建 JAR" -ForegroundColor Red
}

Write-Host "完成!" -ForegroundColor Green
