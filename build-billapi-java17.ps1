# PowerShell script to build billapi with Java 1.7
param()

$ErrorActionPreference = "Stop"

Write-Host "========================================" -ForegroundColor Green
Write-Host "使用 Java 1.7 编译和打包 billapi" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# 设置 Java 1.7 路径
$javaHome = "C:\Work\java\jdk\jdk1.7.0_06"
$javac = "$javaHome\bin\javac.exe"
$jar = "$javaHome\bin\jar.exe"

# 检查 Java 1.7 是否存在
if (!(Test-Path $javac)) {
    Write-Host "错误: 找不到 Java 1.7 编译器: $javac" -ForegroundColor Red
    exit 1
}

# 设置项目路径
$projectRoot = "C:\Work\workspace\A660"
$billapiSrc = "$projectRoot\billapi\src"
$billapiResource = "$projectRoot\billapi\resource"
$buildDir = "$projectRoot\build"
$classesDir = "$buildDir\classes\billapi"
$outputDir = "$projectRoot\output"
$libDir = "$projectRoot\lib"

Write-Host "项目根目录: $projectRoot" -ForegroundColor Cyan
Write-Host "源码目录: $billapiSrc" -ForegroundColor Cyan
Write-Host "输出目录: $outputDir" -ForegroundColor Cyan

# 创建必要的目录
Write-Host "创建构建目录..." -ForegroundColor Yellow
if (!(Test-Path $buildDir)) { New-Item -ItemType Directory -Path $buildDir -Force | Out-Null }
if (!(Test-Path $outputDir)) { New-Item -ItemType Directory -Path $outputDir -Force | Out-Null }

# 清理之前的构建
if (Test-Path $classesDir) {
    Write-Host "清理之前的构建文件..." -ForegroundColor Yellow
    Remove-Item -Path $classesDir -Recurse -Force
}
New-Item -ItemType Directory -Path $classesDir -Force | Out-Null

# 检查源码目录
if (!(Test-Path $billapiSrc)) {
    Write-Host "错误: 找不到源码目录 $billapiSrc" -ForegroundColor Red
    exit 1
}

# 构建 classpath
$classpath = "."
if (Test-Path $libDir) {
    Write-Host "添加 lib 目录到 classpath..." -ForegroundColor Yellow
    $jarFiles = Get-ChildItem -Path $libDir -Filter "*.jar" -Recurse
    foreach ($jarFile in $jarFiles) {
        $classpath += ";$($jarFile.FullName)"
    }
}

# 添加当前项目的 classes 目录
$projectClasses = "$projectRoot\classes"
if (Test-Path $projectClasses) {
    $classpath += ";$projectClasses"
}

Write-Host "使用的 classpath: $classpath" -ForegroundColor Cyan

# 查找所有 Java 源文件
Write-Host "查找 Java 源文件..." -ForegroundColor Yellow
$javaFiles = Get-ChildItem -Path $billapiSrc -Filter "*.java" -Recurse
if ($javaFiles.Count -eq 0) {
    Write-Host "错误: 在 $billapiSrc 中没有找到 Java 源文件" -ForegroundColor Red
    exit 1
}

Write-Host "找到 $($javaFiles.Count) 个 Java 源文件" -ForegroundColor Green

# 创建源文件列表
$sourceList = "$buildDir\sources.txt"
$javaFiles | ForEach-Object { $_.FullName } | Out-File -FilePath $sourceList -Encoding UTF8

# 编译 Java 源码
Write-Host "开始编译 billapi 源码..." -ForegroundColor Yellow
$compileArgs = @(
    "-cp", $classpath,
    "-d", $classesDir,
    "-encoding", "UTF-8",
    "-source", "1.7",
    "-target", "1.7",
    "-Xlint:deprecation",
    "@$sourceList"
)

Write-Host "执行编译命令: $javac $($compileArgs -join ' ')" -ForegroundColor Cyan

$compileResult = & $javac $compileArgs 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Host "编译失败:" -ForegroundColor Red
    Write-Host $compileResult -ForegroundColor Red
    exit 1
}

Write-Host "编译成功!" -ForegroundColor Green

# 复制资源文件
if (Test-Path $billapiResource) {
    Write-Host "复制资源文件..." -ForegroundColor Yellow
    Copy-Item -Path "$billapiResource\*" -Destination $classesDir -Recurse -Force
}

# 创建 JAR 文件
$jarName = "Aisino-A6-billapi-7.1.jar"
$jarPath = "$outputDir\$jarName"

Write-Host "创建 JAR 文件: $jarName" -ForegroundColor Yellow

# 创建 manifest 文件
$manifestFile = "$buildDir\MANIFEST.MF"
$manifestContent = @"
Manifest-Version: 1.0
Built-By: $env:USERNAME
Built-Date: $(Get-Date -Format "yyyyMMdd.HHmm")
Implementation-Title: billapi
Implementation-Version: 7.1

"@

Set-Content -Path $manifestFile -Value $manifestContent -Encoding UTF8

# 打包 JAR
Write-Host "执行打包命令..." -ForegroundColor Yellow
$jarArgs = @("cfm", $jarPath, $manifestFile, "-C", $classesDir, ".")
$jarResult = & $jar $jarArgs 2>&1

if ($LASTEXITCODE -ne 0) {
    Write-Host "JAR 打包失败:" -ForegroundColor Red
    Write-Host $jarResult -ForegroundColor Red
    exit 1
}

Write-Host "========================================" -ForegroundColor Green
Write-Host "构建完成!" -ForegroundColor Green
Write-Host "JAR 文件位置: $jarPath" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# 显示文件信息
if (Test-Path $jarPath) {
    $jarInfo = Get-Item $jarPath
    Write-Host "文件大小: $($jarInfo.Length) 字节" -ForegroundColor Cyan
    Write-Host "创建时间: $($jarInfo.CreationTime)" -ForegroundColor Cyan
}

# 清理临时文件
Remove-Item -Path $sourceList -Force -ErrorAction SilentlyContinue
Remove-Item -Path $manifestFile -Force -ErrorAction SilentlyContinue

Write-Host "billapi 构建完成!" -ForegroundColor Green
